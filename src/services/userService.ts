import { and, eq } from 'drizzle-orm';
import { db } from '../db';
import { chat_room_members, users } from '../db/schema';
import { decrypt<PERSON>ield } from '../db/custom/pgcrypto';

export const getUserByUserId = async (user_id: number) => {
  return await db
    .select({
      first_name: await decrypt<PERSON>ield(users.first_name),
      last_name: await decrypt<PERSON><PERSON>(users.last_name),
      email: users.email,
      email2: users.email_2,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status
    })
    .from(users)
    .where(eq(users.user_id, user_id))
    .then((res) => {
      return res[0];
    });
};

export const getUserByGuid = async (user_guid: string) => {
  return await db
    .select({
      first_name: await decrypt<PERSON>ield(users.first_name),
      last_name: await decrypt<PERSON><PERSON>(users.last_name),
      email: users.email,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status
    })
    .from(users)
    .where(eq(users.user_guid, user_guid))
    .then((res) => {
      return res[0];
    });
};

export const updateOtp = async (user_id: number, otp: string) => {
  return await db
    .update(users)
    .set({ otp: JSON.stringify({ code: otp, created_at: Date.now() }) })
    .where(eq(users.user_id, user_id));
};

export const verifyOtp = async (user_id: string, otp: string) => {
  return await db
    .select({
      first_name: users.first_name,
      last_name: users.last_name,
      email: users.email,
      user_id: users.user_id,
      status: users.status,
      otp: users.otp
    })
    .from(users)
    .where(eq(users.user_guid, user_id))
    .then((res) => {
      if (res) {
        const dbotp = JSON.parse(res[0].otp as string);
        if (dbotp.code === otp) {
          return res[0];
        }
      }

      return null;
    });
};

export const getUserByRoomId = async (
  room_id: number,
  role: 'USER' | 'BUSER'
) => {
  return await db
    .select({
      first_name: await decryptField(users.first_name),
      last_name: await decryptField(users.last_name),
      email: users.email,
      email2: users?.email_2,
      phone: users.phone,
      user_id: users.user_id,
      status: users.status,
      user_guid: users.user_guid
    })
    .from(users)
    .leftJoin(chat_room_members, eq(users.user_id, chat_room_members.user_id))
    .where(and(eq(chat_room_members.room_id, room_id), eq(users.role, role)))
    .then((res) => {
      return res[0];
    });
};

import ApiResponse from '../../helper/ApiResponse';
import asyncHandler from '../../helper/asyncHandler';
import { Request, Response } from 'express';
import {
  getUserByRoomId,
  verifyOtp,
  getUserByGuid,
  getUserByEmailAndRole,
  verifyOtpByEmail,
  getPatientOrders
} from '../../services/userService';
import { createJWT, sendOtp } from './auth.helper';
import { getChatRoomByIdentifier } from '../../services/chatRoomService';
import config from '../../config';

export const authSendotp = asyncHandler(async (req: Request, res: Response) => {
  const room_identifier = req.body.room_identifier;
  const room = await getChatRoomByIdentifier(room_identifier);
  const user = await getUserByRoomId(room.id, 'USER');
  if (user) {
    const email = user.email2 ?? user.email;
    await sendOtp(user.user_id, user.phone, email, user.first_name as string);
    return res
      .status(200)
      .json(
        new ApiResponse(200, { user, room }, 'OTP has been sent to your email')
      );
  } else {
    return res
      .status(200)
      .json(new ApiResponse(400, req.body, 'User not found'));
  }
});

export const authSendOtpByRoleAndEmail = asyncHandler(
  async (req: Request, res: Response) => {
    const { role, room_identifier, email } = req.body;

    // Validate required fields
    if (!role || !room_identifier || !email) {
      return res
        .status(400)
        .json(
          new ApiResponse(
            400,
            req.body,
            'Role, room_identifier, and email are required'
          )
        );
    }

    try {
      // Validate room exists
      const room = await getChatRoomByIdentifier(room_identifier);
      if (!room) {
        return res
          .status(404)
          .json(new ApiResponse(404, req.body, 'Room not found'));
      }

      // Get user by email and role
      const user = await getUserByEmailAndRole(email, role);
      if (!user) {
        return res
          .status(404)
          .json(
            new ApiResponse(
              404,
              req.body,
              'User not found with the specified email and role'
            )
          );
      }

      // Send OTP
      const userEmail = user.email2 ?? user.email;
      await sendOtp(
        user.user_id,
        user.phone,
        userEmail,
        user.first_name as string
      );

      return res.status(200).json(
        new ApiResponse(
          200,
          {
            user: {
              user_guid: user.user_guid,
              email: userEmail,
              role: user.role
            },
            room
          },
          'OTP has been sent to your email'
        )
      );
    } catch (error) {
      return res
        .status(500)
        .json(new ApiResponse(500, req.body, 'Internal server error'));
    }
  }
);

export const authVerifyOtp = asyncHandler(
  async (req: Request, res: Response) => {
    const user_guid = req.body.user_id;
    const otp = req.body.otp;
    let user;
    if (otp === config.TWILIO_DEFAULT_OTP) {
      user = await getUserByGuid(user_guid);
    } else {
      user = await verifyOtp(user_guid, otp);
    }

    if (user) {
      const token = createJWT({
        user_id: user.user_id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email
      });
      return res
        .status(200)
        .json(new ApiResponse(200, { user, token }, 'verified successfully'));
    } else {
      return res
        .status(200)
        .json(new ApiResponse(400, req.body, 'please enter valid otp'));
    }
  }
);

export const authSendOtpByRoleEmail = asyncHandler(
  async (req: Request, res: Response) => {
    const { role, email } = req.body;

    // Validate required fields
    if (!role || !email) {
      return res
        .status(400)
        .json(new ApiResponse(400, req.body, 'Role and email are required'));
    }

    try {
      // Get user by email and role
      const user = await getUserByEmailAndRole(email, role);
      if (!user) {
        return res
          .status(404)
          .json(
            new ApiResponse(
              404,
              req.body,
              'User not found with the specified email and role'
            )
          );
      }

      // Send OTP
      const userEmail = user.email2 ?? user.email;
      await sendOtp(
        user.user_id,
        user.phone,
        userEmail,
        user.first_name as string
      );

      return res.status(200).json(
        new ApiResponse(
          200,
          {
            email: userEmail,
            role: user.role,
            user_guid: user.user_guid
          },
          'OTP has been sent to your email'
        )
      );
    } catch (error) {
      return res
        .status(500)
        .json(new ApiResponse(500, req.body, 'Internal server error'));
    }
  }
);

export const authVerifyOtpByEmail = asyncHandler(
  async (req: Request, res: Response) => {
    const { email, otp } = req.body;

    // Validate required fields
    if (!email || !otp) {
      return res
        .status(400)
        .json(new ApiResponse(400, req.body, 'Email and OTP are required'));
    }

    try {
      let user;
      if (otp === config.TWILIO_DEFAULT_OTP) {
        // For testing purposes, get user by email without OTP verification
        const users =
          (await getUserByEmailAndRole(email, 'USER')) ||
          (await getUserByEmailAndRole(email, 'BUSER')) ||
          (await getUserByEmailAndRole(email, 'AUSER'));
        user = users;
      } else {
        user = await verifyOtpByEmail(email, otp);
      }

      if (user) {
        const token = createJWT({
          user_id: user.user_id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email
        });
        return res
          .status(200)
          .json(
            new ApiResponse(200, { user, token }, 'OTP verified successfully')
          );
      } else {
        return res
          .status(400)
          .json(new ApiResponse(400, req.body, 'Invalid email or OTP'));
      }
    } catch (error) {
      return res
        .status(500)
        .json(new ApiResponse(500, req.body, 'Internal server error'));
    }
  }
);

export const authResendOtp = asyncHandler(
  async (req: Request, res: Response) => {
    return res.status(200).json(new ApiResponse(200, null, 'Success'));
  }
);

export const getPatientOrderList = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Get patient ID from the authenticated user (from JWT token)
      const patientId = req.user.user_id;
      const userRole = req.user.role;

      // Validate that the user is a patient (USER role)
      if (userRole !== 'USER') {
        return res
          .status(403)
          .json(
            new ApiResponse(
              403,
              null,
              'Access denied. This endpoint is only for patients.'
            )
          );
      }

      // Get all orders for this patient
      const orders = await getPatientOrders(patientId);

      if (!orders || orders.length === 0) {
        return res.status(200).json(
          new ApiResponse(
            200,
            {
              patient: {
                user_id: patientId,
                name: `${req.user.first_name} ${req.user.last_name}`,
                email: req.user.email,
                role: userRole
              },
              orders: [],
              total_orders: 0
            },
            'No orders found for this patient'
          )
        );
      }

      // Format the response with patient and order details
      const response = {
        patient: {
          user_id: patientId,
          name: `${req.user.first_name} ${req.user.last_name}`,
          email: req.user.email,
          role: userRole
        },
        orders: orders.map((order) => ({
          id: order.id,
          order_guid: order.order_guid,
          service_id: order.service_id,
          provider_id: order.provider_id,
          status: order.status,
          service_type: order.service_type,
          session_type: order.session_type,
          created_at: order.created_at,
          updated_at: order.updated_at,
          prescription_delivery: order.prescription_delivery,
          ravkoo_prescription_option: order.ravkoo_prescription_option,
          pharmacy_details: {
            name: order.pharmacy_name,
            phone: order.pharmacy_phone,
            address: order.pharmacy_address
          }
        })),
        total_orders: orders.length
      };

      return res
        .status(200)
        .json(
          new ApiResponse(
            200,
            response,
            'Patient orders retrieved successfully'
          )
        );
    } catch (error) {
      console.error('Error retrieving patient orders:', error);
      return res
        .status(500)
        .json(new ApiResponse(500, null, 'Internal server error'));
    }
  }
);

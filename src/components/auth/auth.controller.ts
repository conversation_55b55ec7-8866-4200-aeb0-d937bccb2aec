import ApiResponse from '../../helper/ApiResponse';
import asyncHandler from '../../helper/asyncHandler';
import { Request, Response } from 'express';
import {
  getUserByUserId,
  getUserByRoomId,
  verifyOtp,
  getUserByGuid
} from '../../services/userService';
import { createJWT, generateOtp, sendOtp } from './auth.helper';
import { getChatRoomByIdentifier } from '../../services/chatRoomService';
import config from '../../config';

export const authSendotp = asyncHandler(async (req: Request, res: Response) => {
  const room_identifier = req.body.room_identifier;
  const room = await getChatRoomByIdentifier(room_identifier);
  const user = await getUserByRoomId(room.id, 'USER');
  if (user) {
    const email = user.email2 ?? user.email;
    await sendOtp(
      user.user_id,
      user.phone,
      email,
      user.first_name as string
    );
    return res
      .status(200)
      .json(new ApiResponse(200, {user, room}, 'OTP has been sent to your email'));
  } else {
    return res
      .status(200)
      .json(new ApiResponse(400, req.body, 'User not found'));
  }
});

export const authVerifyOtp = asyncHandler(
  async (req: Request, res: Response) => {
    const user_guid = req.body.user_id;
    const otp = req.body.otp;
    let user;
    if (otp === config.TWILIO_DEFAULT_OTP) {
      user = await getUserByGuid(user_guid);
    } else {
      user = await verifyOtp(user_guid, otp);
    }

    if (user) {
      const token = createJWT({
        user_id: user.user_id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email
      });
      return res
        .status(200)
        .json(new ApiResponse(200, { user, token }, 'verified successfully'));
    } else {
      return res
        .status(200)
        .json(new ApiResponse(400, req.body, 'please enter valid otp'));
    }
  }
);

export const authResendOtp = asyncHandler(
  async (req: Request, res: Response) => {
    return res.status(200).json(new ApiResponse(200, null, 'Success'));
  }
);

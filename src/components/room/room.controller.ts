import ApiResponse from '../../helper/ApiResponse';
import asyncHandler from '../../helper/asyncHandler';
import { Request, Response } from 'express';
import config from '../../config';
import { getUserByUserId } from '../../services/userService';
import { db } from '../../db';
import { chat_room_members, chat_rooms } from '../../db/schema';
import { and, eq, or } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';
import ApiError from '../../helper/ApiError';
import { sendChatLinkEmail } from '../../mail/mail.helper';
import { sendSMSMessage } from './room.helper';

export const getRoom = asyncHandler(async (req: Request, res: Response) => {
  return res.status(200).json(new ApiResponse(200, null, 'Success'));
});

export const createRoom = asyncHandler(async (req: Request, res: Response) => {
  const { service_key, description, patient_id, provider_id, sms_status } =
    req.body;

  const parent = alias(chat_room_members, 'patient');
  const provider = alias(chat_room_members, 'provider');

  const [isRoomExists] = await db
    .select()
    .from(chat_rooms)
    .innerJoin(parent, eq(chat_rooms.id, parent.room_id))
    .innerJoin(provider, eq(chat_rooms.id, provider.room_id))
    .where(
      and(
        eq(chat_rooms.service_key, service_key),
        eq(parent.user_id, patient_id),
        eq(provider.user_id, provider_id)
      )
    );

  let roomIdentifier;

  if (isRoomExists) {
    roomIdentifier = isRoomExists.chat_rooms.room_identifier;
  } else {
    const [room] = await db
      .insert(chat_rooms)
      .values({
        room_name: `patient_chat_provider+${patient_id}+${provider_id}`,
        service_key: service_key,
        description: description,
        deleted: false
      })
      .returning({
        id: chat_rooms.id,
        room_identifier: chat_rooms.room_identifier
      });

    roomIdentifier = room.room_identifier;

    await db.insert(chat_room_members).values([
      {
        user_id: patient_id,
        room_id: room.id
      },
      {
        user_id: provider_id,
        room_id: room.id
      }
    ]);
  }

  const patient = await getUserByUserId(patient_id);
  if (sms_status && patient) {
    const chatLink = `${config.WEBSITE_URL}/chat/patient/${roomIdentifier}`;
    const fullName = patient.first_name + ' ' + patient.last_name;
    sendSMSMessage(patient.phone, fullName, chatLink);
    const email = patient?.email2 ?? patient?.email;
    if (email) {
      await sendChatLinkEmail(email, fullName, chatLink);
    }
  }

  const roomUrl =
    config.WEBSITE_URL + '/api/auth/provider-redirect?room=' + roomIdentifier;

  return res.status(200).json(new ApiResponse(200, { roomUrl }, 'Success'));
});

export const createRoomPatient = asyncHandler(
  async (req: Request, res: Response) => {
    const { service_key, description, patient_id, provider_id } = req.body;

    const parent = alias(chat_room_members, 'patient');
    const provider = alias(chat_room_members, 'provider');

    const [isRoomExists] = await db
      .select()
      .from(chat_rooms)
      .innerJoin(parent, eq(chat_rooms.id, parent.room_id))
      .innerJoin(provider, eq(chat_rooms.id, provider.room_id))
      .where(
        and(
          eq(chat_rooms.service_key, service_key),
          eq(parent.user_id, patient_id),
          eq(provider.user_id, provider_id)
        )
      );

    let roomIdentifier;

    if (isRoomExists) {
      roomIdentifier = isRoomExists.chat_rooms.room_identifier;
    } else {
      const [room] = await db
        .insert(chat_rooms)
        .values({
          room_name: `patient_chat_provider+${patient_id}+${provider_id}`,
          service_key: service_key,
          description: description,
          deleted: false
        })
        .returning({
          id: chat_rooms.id,
          room_identifier: chat_rooms.room_identifier
        });

      roomIdentifier = room.room_identifier;

      await db.insert(chat_room_members).values([
        {
          user_id: patient_id,
          room_id: room.id
        },
        {
          user_id: provider_id,
          room_id: room.id
        }
      ]);
    }

    // const patient = await getUserByUserId(patient_id);
    // if (sms_status && patient) {
    //   const chatLink = `${config.WEBSITE_URL}/chat/patient/${roomIdentifier}`;
    //   const fullName = patient.first_name + ' ' + patient.last_name;
    //   sendSMSMessage(patient.phone, fullName, chatLink);
    //   const email = patient?.email2 ?? patient?.email;
    //   if (email) {
    //     await sendChatLinkEmail(email, fullName, chatLink);
    //   }
    // }

    const roomUrl =
      config.WEBSITE_URL + '/api/auth/patient-redirect?room=' + roomIdentifier;

    return res.status(200).json(new ApiResponse(200, { roomUrl }, 'Success'));
  }
);

export const validateRoomForPatients = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;

    const room = await db.query.chat_rooms.findMany({
      where: (chat_rooms, { eq, and }) =>
        and(eq(chat_rooms.room_identifier, roomIdentifier)),
      with: {
        chat_room_members: {
          with: {
            user: {
              columns: {
                user_id: true,
                role: true
              }
            }
          }
        }
      }
    });

    return res.status(200).json(new ApiResponse(200, room, 'Success'));
  }
);

export const existsConsultForPatientsChatRoom = asyncHandler(
  async (req: Request, res: Response) => {
    const roomIdentifier = req.params.roomId;

    const room = await db.query.chat_rooms.findFirst({
      where: (chat_rooms, { eq, and }) =>
        and(eq(chat_rooms.room_identifier, roomIdentifier)),
      with: {
        chat_room_members: {
          with: {
            user: {
              columns: {
                user_id: true,
                role: true
              }
            }
          }
        }
      }
    });

    const provider = room?.chat_room_members?.find(
      (member) => member.user.role === 'BUSER'
    );
    const providerId = provider ? provider.user.user_id : null;

    const user = room?.chat_room_members?.find(
      (member) => member.user.role === 'USER'
    );
    const userId = provider ? user?.user.user_id : null;

    if (!providerId || !userId) {
      throw new ApiError(404, 'Wrong Chat Room');
    }

    const consultExists = await db.query.telehealth_service_order.findMany({
      where: (telehealth_service_order, { eq, and }) =>
        and(
          eq(telehealth_service_order.answer_given_by, userId),
          eq(telehealth_service_order.provider_id, providerId),
          eq(telehealth_service_order.status, 'accept')
        ),
      columns: {
        id: true,
        service_id: true,
        provider_id: true,
        status: true,
        session_type: true
      },
      with: {
        telehealth_service: {
          columns: {
            id: true,
            service_name: true,
            service_type: true,
            service_key: true,
            session_type: true
          }
        }
      }
    });

    return res
      .status(200)
      .json(new ApiResponse(200, { consultExists }, 'Success'));
  }
);

export const enableChatRoom = asyncHandler(
  async (req: Request, res: Response) => {
    const roomId = req.params.roomId;

    if (!roomId) {
      throw new ApiError(400, 'Room ID is required');
    }

    const [room] = await db
      .update(chat_rooms)
      .set({ active: true })
      .where(eq(chat_rooms.id, Number(roomId)))
      .returning();

    if (!room) {
      throw new ApiError(404, 'Chat room not found');
    }

    return res
      .status(200)
      .json(new ApiResponse(200, room, 'Chat room enabled'));
  }
);

export const disableChatRoom = asyncHandler(
  async (req: Request, res: Response) => {
    const roomId = req.params.roomId;

    if (!roomId) {
      throw new ApiError(400, 'Room ID is required');
    }

    const [room] = await db
      .update(chat_rooms)
      .set({ active: false })
      .where(eq(chat_rooms.id, Number(roomId)))
      .returning();

    if (!room) {
      throw new ApiError(404, 'Chat room not found');
    }

    return res
      .status(200)
      .json(new ApiResponse(200, room, 'Chat room disabled'));
  }
);

import { Router } from 'express';

import {
  existsConsultForPatientsChatRoom,
  getRoom,
  createRoom,
  validateRoomForPatients,
  createRoomPatient,
  enableChatRoom,
  disableChatRoom
} from './room.controller';

const router = Router();

router.route('/create').get(getRoom).post(createRoom);
router.route('/create/patient').post(createRoomPatient);
router.route('/:roomId/validate').get(validateRoomForPatients);
router.route('/:roomId/exits-consult').get(existsConsultForPatientsChatRoom);
router.route('/:roomId/enable').post(enableChatRoom);
router.route('/:roomId/disable').post(disableChatRoom);

export default router;
